<?php

namespace App\Controllers;

class Auth extends BaseController
{
    public function login()
    {
        // Handle login form submission
        if ($this->request->getMethod() === 'POST') {
            $email = $this->request->getPost('email');
            $password = $this->request->getPost('password');
            $remember = $this->request->getPost('remember');
            
            // Basic validation
            $validation = \Config\Services::validation();
            $validation->setRules([
                'email' => 'required|valid_email',
                'password' => 'required|min_length[6]'
            ]);
            
            if (!$validation->withRequest($this->request)->run()) {
                $data = [
                    'title' => 'Dakoii Commodity Buyer',
                    'description' => 'Modern commodity trading platform for agricultural products',
                    'errors' => $validation->getErrors()
                ];
                return view('landing_page', $data);
            }
            
            // TODO: Implement actual authentication logic here
            // For now, just redirect back with a message
            
            // Simulate login check (replace with actual authentication)
            if ($email === '<EMAIL>' && $password === 'password123') {
                // Set session data
                session()->set([
                    'user_id' => 1,
                    'email' => $email,
                    'logged_in' => true
                ]);
                
                return redirect()->to('/dashboard')->with('success', 'Login successful!');
            } else {
                return redirect()->back()->withInput()->with('error', 'Invalid email or password.');
            }
        }
        
        // If GET request, redirect to home page
        return redirect()->to('/');
    }
    
    public function logout()
    {
        session()->destroy();
        return redirect()->to('/')->with('success', 'You have been logged out successfully.');
    }
    
    public function dashboard()
    {
        // Check if user is logged in
        if (!session()->get('logged_in')) {
            return redirect()->to('/')->with('error', 'Please login to access the dashboard.');
        }
        
        $data = [
            'title' => 'Dashboard - DCBuyer',
            'user_email' => session()->get('email')
        ];
        
        return view('dashboard', $data);
    }
}
